<?php
/**
 * 用户认证API
 */

define('IDATAS_SYSTEM', true);
require_once '../config/config.php';
require_once '../includes/UserManager.php';

// 获取请求方法和动作
$method = $_SERVER['REQUEST_METHOD'];
$action = $_GET['action'] ?? '';

try {
    switch ($action) {
        case 'register':
            handleRegister();
            break;
            
        case 'login':
            handleLogin();
            break;
            
        case 'validate':
            handleValidate();
            break;
            
        case 'info':
            handleUserInfo();
            break;
            
        case 'kami':
            handleKami();
            break;
            
        case 'stats':
            handleStats();
            break;
            
        default:
            Utils::jsonResponse(CODE_BAD_REQUEST, '无效的操作');
    }
} catch (Exception $e) {
    Utils::log("API错误: " . $e->getMessage(), 'ERROR');
    Utils::jsonResponse(CODE_SERVER_ERROR, MSG_SERVER_ERROR);
}

/**
 * 处理用户注册
 */
function handleRegister() {
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        Utils::jsonResponse(CODE_BAD_REQUEST, '请使用POST方法');
    }
    
    $input = json_decode(file_get_contents('php://input'), true);
    $username = $input['username'] ?? '';
    $password = $input['password'] ?? '';
    
    $result = UserManager::register($username, $password);
    
    if ($result['success']) {
        Utils::jsonResponse(CODE_SUCCESS, $result['message'], $result['data']);
    } else {
        $code = ($result['message'] === MSG_USER_EXISTS) ? CODE_CONFLICT : CODE_BAD_REQUEST;
        Utils::jsonResponse($code, $result['message']);
    }
}

/**
 * 处理用户登录
 */
function handleLogin() {
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        Utils::jsonResponse(CODE_BAD_REQUEST, '请使用POST方法');
    }
    
    $input = json_decode(file_get_contents('php://input'), true);
    $username = $input['username'] ?? '';
    $password = $input['password'] ?? '';
    
    $result = UserManager::login($username, $password);
    
    if ($result['success']) {
        Utils::jsonResponse(CODE_SUCCESS, $result['message'], $result['data']);
    } else {
        Utils::jsonResponse(CODE_UNAUTHORIZED, $result['message']);
    }
}

/**
 * 处理Token验证
 */
function handleValidate() {
    $token = $_GET['token'] ?? '';
    
    if (empty($token)) {
        Utils::jsonResponse(CODE_BAD_REQUEST, '缺少Token参数');
    }
    
    $user = UserManager::getUserByToken($token);
    
    if ($user) {
        Utils::jsonResponse(CODE_SUCCESS, 'Token有效', [
            'username' => $user['username'],
            'token' => $user['token'],
            'vip_code' => $user['vip_code'],
            'vip_time' => $user['vip_time']
        ]);
    } else {
        Utils::jsonResponse(CODE_UNAUTHORIZED, MSG_TOKEN_INVALID);
    }
}

/**
 * 处理获取用户信息
 */
function handleUserInfo() {
    $token = $_GET['token'] ?? '';
    
    if (empty($token)) {
        Utils::jsonResponse(CODE_BAD_REQUEST, '缺少Token参数');
    }
    
    $user = UserManager::getUserByToken($token);
    
    if (!$user) {
        Utils::jsonResponse(CODE_UNAUTHORIZED, MSG_TOKEN_INVALID);
    }
    
    // 调用上游API获取详细信息
    $upstreamResult = Utils::httpRequest(UPSTREAM_INFO_URL, ['token' => $token]);
    
    if ($upstreamResult['success'] && $upstreamResult['data']['code'] === 200) {
        $upstreamData = $upstreamResult['data'];
        
        // 合并本地和上游数据
        $responseData = [
            'user' => [
                'username' => $user['username'],
                'token' => $user['token'],
                'created_time' => $user['created_time'],
                'last_login' => $user['last_login'],
                'login_count' => $user['login_count'],
                'vip_code' => $user['vip_code'],
                'vip_time' => $user['vip_time']
            ],
            'msg' => $upstreamData['msg'] ?? null,
            'today_launch_count' => $upstreamData['today_launch_count'] ?? 0
        ];
        
        Utils::jsonResponse(CODE_SUCCESS, MSG_SUCCESS, $responseData);
    } else {
        // 如果上游API失败，返回本地数据
        Utils::jsonResponse(CODE_SUCCESS, MSG_SUCCESS, [
            'user' => [
                'username' => $user['username'],
                'token' => $user['token'],
                'created_time' => $user['created_time'],
                'last_login' => $user['last_login'],
                'login_count' => $user['login_count'],
                'vip_code' => $user['vip_code'],
                'vip_time' => $user['vip_time']
            ]
        ]);
    }
}

/**
 * 处理卡密使用
 */
function handleKami() {
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        Utils::jsonResponse(CODE_BAD_REQUEST, '请使用POST方法');
    }
    
    $input = json_decode(file_get_contents('php://input'), true);
    $token = $input['token'] ?? '';
    $kami = $input['kami'] ?? '';
    
    if (empty($token) || empty($kami)) {
        Utils::jsonResponse(CODE_BAD_REQUEST, '缺少必要参数');
    }
    
    $result = UserManager::useKami($token, $kami);
    
    if ($result['success']) {
        Utils::jsonResponse(CODE_SUCCESS, $result['message'], $result['data']);
    } else {
        Utils::jsonResponse(CODE_BAD_REQUEST, $result['message']);
    }
}

/**
 * 处理统计信息
 */
function handleStats() {
    $stats = UserManager::getUserStats();
    Utils::jsonResponse(CODE_SUCCESS, MSG_SUCCESS, $stats);
}
?>
