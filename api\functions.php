<?php
/**
 * 功能查询API
 */

define('IDATAS_SYSTEM', true);
require_once '../config/config.php';
require_once '../includes/UserManager.php';

// 获取请求方法和动作
$method = $_SERVER['REQUEST_METHOD'];
$action = $_GET['action'] ?? '';

try {
    // 验证Token
    $token = $_GET['token'] ?? '';
    if (empty($token)) {
        Utils::jsonResponse(CODE_BAD_REQUEST, '缺少Token参数');
    }
    
    if (!UserManager::validateToken($token)) {
        Utils::jsonResponse(CODE_UNAUTHORIZED, MSG_TOKEN_INVALID);
    }
    
    switch ($action) {
        case 'basic_query':
            handleBasicQuery();
            break;
            
        case 'social_query':
            handleSocialQuery();
            break;
            
        default:
            Utils::jsonResponse(CODE_BAD_REQUEST, '无效的操作');
    }
} catch (Exception $e) {
    Utils::log("功能API错误: " . $e->getMessage(), 'ERROR');
    Utils::jsonResponse(CODE_SERVER_ERROR, MSG_SERVER_ERROR);
}

/**
 * 处理基础信息查询
 */
function handleBasicQuery() {
    $token = $_GET['token'];
    $name = $_GET['name'] ?? '';
    $idcard = $_GET['idcard'] ?? '';
    $lx = $_GET['lx'] ?? '';
    
    // 验证必要参数
    if (empty($name) || empty($idcard) || empty($lx)) {
        Utils::jsonResponse(CODE_BAD_REQUEST, '缺少必要参数：name, idcard, lx');
    }
    
    // 验证参数格式
    if (!Utils::validateName($name)) {
        Utils::jsonResponse(CODE_BAD_REQUEST, '姓名格式错误');
    }
    
    if (!Utils::validateIdCard($idcard)) {
        Utils::jsonResponse(CODE_BAD_REQUEST, '身份证号格式错误');
    }
    
    if (!in_array($lx, ['1', '2'])) {
        Utils::jsonResponse(CODE_BAD_REQUEST, '查询类型错误（1=民事诉讼，2=户籍信息）');
    }
    
    // 记录查询日志
    Utils::log("基础信息查询: Token={$token}, Name={$name}, IDCard={$idcard}, Type={$lx}", 'INFO');
    
    // 调用上游API
    $params = [
        'token' => $token,
        'name' => $name,
        'idcard' => $idcard,
        'lx' => $lx
    ];
    
    $result = Utils::httpRequest(BASIC_QUERY_URL, $params);
    
    if (!$result['success']) {
        Utils::jsonResponse(CODE_SERVER_ERROR, '查询服务暂时不可用');
    }
    
    $data = $result['data'];
    
    if (!$data || !isset($data['code'])) {
        Utils::jsonResponse(CODE_SERVER_ERROR, 'API响应格式错误');
    }
    
    // 处理响应数据
    if ($data['code'] === 200) {
        // 成功响应
        $responseData = [
            'name' => $data['data']['name'] ?? $name,
            'idcard' => $data['data']['idcard'] ?? $idcard,
            'query_type' => $lx,
            'query_type_name' => $lx === '1' ? '民事诉讼信息查询' : '户籍信息查询',
            'fake_address' => $data['data']['fake_address'] ?? '',
            'real_address' => $data['data']['real_address'] ?? '',
            'image_url' => $data['data']['image_url'] ?? '',
            'query_time' => date('Y-m-d H:i:s')
        ];
        
        Utils::jsonResponse(CODE_SUCCESS, '查询成功', $responseData);
    } else {
        // 错误响应
        $message = $data['message'] ?? '查询失败';
        Utils::jsonResponse($data['code'], $message);
    }
}

/**
 * 处理社交媒体信息查询
 */
function handleSocialQuery() {
    $token = $_GET['token'];
    $wechat = $_GET['wechat'] ?? '';
    $douyin = $_GET['douyin'] ?? '';
    $kuaishou = $_GET['kuaishou'] ?? '';
    
    // 至少需要一个社交媒体账号
    if (empty($wechat) && empty($douyin) && empty($kuaishou)) {
        Utils::jsonResponse(CODE_BAD_REQUEST, '至少需要提供一个社交媒体账号');
    }
    
    // 记录查询日志
    $accounts = [];
    if ($wechat) $accounts[] = "WeChat={$wechat}";
    if ($douyin) $accounts[] = "Douyin={$douyin}";
    if ($kuaishou) $accounts[] = "Kuaishou={$kuaishou}";
    
    Utils::log("社交媒体查询: Token={$token}, " . implode(', ', $accounts), 'INFO');
    
    // 构建查询参数
    $params = [
        'action' => 'generate_id',
        'token' => $token
    ];
    
    if ($wechat) $params['wechat'] = $wechat;
    if ($douyin) $params['douyin'] = $douyin;
    if ($kuaishou) $params['kuaishou'] = $kuaishou;
    
    // 调用上游API
    $result = Utils::httpRequest(BASIC_QUERY_URL, $params);
    
    if (!$result['success']) {
        Utils::jsonResponse(CODE_SERVER_ERROR, '查询服务暂时不可用');
    }
    
    $data = $result['data'];
    
    if (!$data || !isset($data['code'])) {
        Utils::jsonResponse(CODE_SERVER_ERROR, 'API响应格式错误');
    }
    
    // 处理响应数据
    if ($data['code'] === 200) {
        // 成功响应
        $responseData = [
            'name' => $data['data']['name'] ?? '',
            'idcard' => $data['data']['idcard'] ?? '',
            'query_accounts' => [
                'wechat' => $wechat,
                'douyin' => $douyin,
                'kuaishou' => $kuaishou
            ],
            'query_time' => date('Y-m-d H:i:s')
        ];
        
        Utils::jsonResponse(CODE_SUCCESS, '查询成功', $responseData);
    } else {
        // 错误响应
        $message = $data['message'] ?? '查询失败';
        Utils::jsonResponse($data['code'], $message);
    }
}

/**
 * 获取功能列表
 */
function getFunctionList() {
    $functions = [
        [
            'id' => 'basic_query',
            'name' => '基础信息查询',
            'description' => '查询个人基本信息，包括姓名、身份证号等',
            'icon' => 'fas fa-search',
            'badge' => '🔑',
            'params' => [
                ['name' => 'name', 'label' => '姓名', 'type' => 'text', 'required' => true],
                ['name' => 'idcard', 'label' => '身份证号', 'type' => 'text', 'required' => true],
                ['name' => 'lx', 'label' => '查询类型', 'type' => 'select', 'required' => true, 'options' => [
                    ['value' => '1', 'label' => '民事诉讼信息查询'],
                    ['value' => '2', 'label' => '户籍信息查询']
                ]]
            ]
        ],
        [
            'id' => 'social_query',
            'name' => '社交媒体查询',
            'description' => '查询抖音、快手、微信等社交媒体账号信息',
            'icon' => 'fas fa-users',
            'badge' => '📱',
            'params' => [
                ['name' => 'wechat', 'label' => '微信号', 'type' => 'text', 'required' => false],
                ['name' => 'douyin', 'label' => '抖音号', 'type' => 'text', 'required' => false],
                ['name' => 'kuaishou', 'label' => '快手号', 'type' => 'text', 'required' => false]
            ]
        ]
    ];
    
    Utils::jsonResponse(CODE_SUCCESS, MSG_SUCCESS, $functions);
}
?>
