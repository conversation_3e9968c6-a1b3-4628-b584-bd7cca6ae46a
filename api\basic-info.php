<?php
/**
 * iDatas社工系统 - 基础信息查询API代理
 */

require_once __DIR__ . '/../includes/functions.php';
require_once __DIR__ . '/../includes/vip.php';

header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// 处理OPTIONS请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

// 获取参数
$token = $_GET['token'] ?? $_POST['token'] ?? '';
$name = $_GET['name'] ?? $_POST['name'] ?? '';
$idcard = $_GET['idcard'] ?? $_POST['idcard'] ?? '';
$lx = $_GET['lx'] ?? $_POST['lx'] ?? '';

// 验证必需参数
if (empty($token) || empty($name) || empty($idcard) || empty($lx)) {
    jsonResponse(400, '缺少必需参数: token, name, idcard, lx');
}

// 验证Token
if (!validateToken($token)) {
    jsonResponse(401, 'Token无效或已过期');
}

// 检查查询限制
checkQueryLimitMiddleware($token);

// 清理输入数据
$name = sanitizeInput($name);
$idcard = sanitizeInput($idcard);
$lx = sanitizeInput($lx);

// 验证身份证号格式
if (!preg_match('/^[0-9]{17}[0-9X]$/', $idcard)) {
    jsonResponse(400, '身份证号格式不正确');
}

// 验证查询类型
if (!in_array($lx, ['1', '2'])) {
    jsonResponse(400, '查询类型无效');
}

// 记录查询日志
writeLog('INFO', '基础信息查询请求', [
    'token' => $token,
    'name' => $name,
    'idcard' => substr($idcard, 0, 6) . '****' . substr($idcard, -4), // 脱敏处理
    'lx' => $lx
]);

// 构建上游API请求
$upstreamUrl = BASIC_INFO_API . '?' . http_build_query([
    'token' => $token,
    'name' => $name,
    'idcard' => $idcard,
    'lx' => $lx
]);

// 调用上游API
$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $upstreamUrl);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_TIMEOUT, 30);
curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
curl_setopt($ch, CURLOPT_USERAGENT, 'iDatas System/1.0');

$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
$error = curl_error($ch);
curl_close($ch);

// 检查请求是否成功
if ($httpCode !== 200 || !$response) {
    writeLog('ERROR', '上游API调用失败', [
        'url' => $upstreamUrl,
        'http_code' => $httpCode,
        'error' => $error
    ]);
    jsonResponse(500, '查询服务暂时不可用，请稍后重试');
}

// 解析响应
$result = json_decode($response, true);

if (!$result) {
    writeLog('ERROR', '上游API响应格式错误', ['response' => $response]);
    jsonResponse(500, '查询服务响应格式错误');
}

// 记录查询结果
$success = isset($result['code']) && $result['code'] == 200;
writeLog('INFO', '基础信息查询结果', [
    'token' => $token,
    'success' => $success
]);

// 记录查询日志（用于统计和限制）
logQuery($token, 'basic_info', $success);

// 返回结果
header('Content-Type: application/json; charset=utf-8');
echo json_encode($result, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
?>
