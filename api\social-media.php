<?php
/**
 * iDatas社工系统 - 社交媒体信息查询API代理
 */

require_once __DIR__ . '/../includes/functions.php';
require_once __DIR__ . '/../includes/vip.php';

header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// 处理OPTIONS请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

// 获取参数
$token = $_GET['token'] ?? $_POST['token'] ?? '';
$wechat = $_GET['wechat'] ?? $_POST['wechat'] ?? '';
$douyin = $_GET['douyin'] ?? $_POST['douyin'] ?? '';
$kuaishou = $_GET['kuaishou'] ?? $_POST['kuaishou'] ?? '';

// 验证必需参数
if (empty($token)) {
    jsonResponse(400, '缺少Token参数');
}

// 验证至少有一个社交媒体账号
if (empty($wechat) && empty($douyin) && empty($kuaishou)) {
    jsonResponse(400, '请至少提供一个社交媒体账号');
}

// 验证Token
if (!validateToken($token)) {
    jsonResponse(401, 'Token无效或已过期');
}

// 检查查询限制
checkQueryLimitMiddleware($token);

// 清理输入数据
$wechat = sanitizeInput($wechat);
$douyin = sanitizeInput($douyin);
$kuaishou = sanitizeInput($kuaishou);

// 记录查询日志
writeLog('INFO', '社交媒体查询请求', [
    'token' => $token,
    'wechat' => !empty($wechat) ? substr($wechat, 0, 3) . '***' : '',
    'douyin' => !empty($douyin) ? substr($douyin, 0, 3) . '***' : '',
    'kuaishou' => !empty($kuaishou) ? substr($kuaishou, 0, 3) . '***' : ''
]);

// 构建查询参数
$params = ['token' => $token, 'action' => 'generate_id'];
if (!empty($wechat)) $params['wechat'] = $wechat;
if (!empty($douyin)) $params['douyin'] = $douyin;
if (!empty($kuaishou)) $params['kuaishou'] = $kuaishou;

// 构建上游API请求
$upstreamUrl = SOCIAL_MEDIA_API . '&' . http_build_query($params);

// 调用上游API
$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $upstreamUrl);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_TIMEOUT, 30);
curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
curl_setopt($ch, CURLOPT_USERAGENT, 'iDatas System/1.0');

$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
$error = curl_error($ch);
curl_close($ch);

// 检查请求是否成功
if ($httpCode !== 200 || !$response) {
    writeLog('ERROR', '上游API调用失败', [
        'url' => $upstreamUrl,
        'http_code' => $httpCode,
        'error' => $error
    ]);
    jsonResponse(500, '查询服务暂时不可用，请稍后重试');
}

// 解析响应
$result = json_decode($response, true);

if (!$result) {
    writeLog('ERROR', '上游API响应格式错误', ['response' => $response]);
    jsonResponse(500, '查询服务响应格式错误');
}

// 记录查询结果
$success = isset($result['code']) && $result['code'] == 200;
writeLog('INFO', '社交媒体查询结果', [
    'token' => $token,
    'success' => $success
]);

// 记录查询日志（用于统计和限制）
logQuery($token, 'social_media', $success);

// 返回结果
header('Content-Type: application/json; charset=utf-8');
echo json_encode($result, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
?>
