<?php
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');
header('Content-Type: application/json; charset=utf-8');
include './verify_vip.php';


// 获取请求中的token
$token = $_GET['token'];  // 或者通过其他方式获取

// 自定义限制（可以自由组合）
$vipTimeLimit = false;  // 是否启用会员时间限制
$vipCodeLimit = true;  // 是否启用会员功能限制

// 定义回调函数
$callback = function($vipcode, $viptime) use ($vipTimeLimit, $vipCodeLimit) {
    if ($vipCodeLimit) {
        // 如果启用会员功能限制，执行相应的检查
        $result = vipCodeLimit($vipcode);
        if ($result !== true) return $result;  // 如果验证失败，返回错误信息
    }

    if ($vipTimeLimit) {
        // 如果启用会员时间限制，执行相应的检查
        $result = vipTimeLimit($viptime);
        if ($result !== true) return $result;  // 如果验证失败，返回错误信息
    }

    return true;  // 如果没有任何限制失败，返回true
};

// 调用验证函数
$verificationResult = verifyVipStatus($token, $callback);

// 如果返回的是错误信息，则输出
if ($verificationResult !== true) {
    echo json_encode($verificationResult, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
    exit;
}



$directory = './luyin/';

// 检查目录是否存在
if (is_dir($directory)) {
    // 获取目录中的文件名
    $files = array_diff(scandir($directory), array('.', '..')); // 排除 . 和 .. 目录
    
    // 检查是否有文件
    if (!empty($files)) {
        // 随机选择一个文件名
        $filename = $filename = $files[array_rand($files)];
         // 如果需要随机选择，使用 array_rand($files)
        
        // 输出 JSON 格式的结果
        echo json_encode([
            "code" => "200",
            "message" => "「获取成功」",
            "mp3url" => "http://" . $_SERVER['HTTP_HOST'] . "/luyin/" . $filename,
        '官方TG频道' => '官方TG频道@idatas8'
        ], JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
    } else {
        // 目录为空的情况
        echo json_encode([
            "code" => "404",
            "message" => "目录中没有可用文件"
        ], JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
    }
} else {
    // 目录不存在的情况
    echo json_encode([
        "code" => "500",
        "message" => "目录不存在，请检查路径"
    ], JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
}
?>
