<?php

include 'db.php';

// 检查并引入专业的身份证和手机号处理包
$vendorPath = __DIR__ . '/../vendor/autoload.php';
$useAdvancedLibs = false;

if (file_exists($vendorPath)) {
    require_once $vendorPath;
    $useAdvancedLibs = true;
}

header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');
header('Content-Type: application/json; charset=utf-8');

// 身份证信息扩展函数（智能选择版本）
function extractIdCardInfo($idCard) {
    global $useAdvancedLibs;

    $info = [];

    // 如果有专业包，优先使用
    if ($useAdvancedLibs && class_exists('Jxlwqq\IdValidator\IdValidator')) {
        try {
            $idValidator = new \Jxlwqq\IdValidator\IdValidator();

            // 验证身份证有效性
            if (!$idValidator->isValid($idCard)) {
                return extractIdCardInfoSimple($idCard); // 降级到简化版本
            }

            // 获取详细信息
            $idInfo = $idValidator->getInfo($idCard);

            if ($idInfo) {
                $info['出生日期'] = $idInfo['birthdayCode'];

                // 计算年龄
                $today = new DateTime();
                $birth = new DateTime($idInfo['birthdayCode']);
                $age = $today->diff($birth)->y;
                $info['年龄'] = $age . '岁';

                // 星座和生肖
                $info['星座'] = $idInfo['constellation'];
                $info['生肖'] = $idInfo['chineseZodiac'];

                // 性别
                $info['性别'] = $idInfo['sex'] == 1 ? '男' : '女';

                // 地址信息
                $info['身份证归属地区'] = $idInfo['address'];
                $info['省市区'] = implode(' > ', $idInfo['addressTree']);

                // 身份证类型
                $info['证件长度'] = $idInfo['length'] . '位';
                if ($idInfo['length'] == 18) {
                    $info['校验码'] = $idInfo['checkBit'];
                }

                // 地址码状态
                $info['地址码状态'] = $idInfo['abandoned'] ? '已废弃' : '正在使用';


            }
        } catch (Exception $e) {
            // 如果专业包出错，使用简化版本
            $info = extractIdCardInfoSimple($idCard);
        }
    } else {
        // 使用简化版本
        $info = extractIdCardInfoSimple($idCard);
    }

    return $info;
}

// 简化版身份证信息提取（备用）
function extractIdCardInfoSimple($idCard) {
    $info = [];
    if (preg_match('/^\d{17}[\dxX]$/', $idCard)) {
        // 提取出生日期
        $birthYear = substr($idCard, 6, 4);
        $birthMonth = substr($idCard, 10, 2);
        $birthDay = substr($idCard, 12, 2);
        $birthDate = $birthYear . '-' . $birthMonth . '-' . $birthDay;

        // 验证日期有效性
        if (checkdate($birthMonth, $birthDay, $birthYear)) {
            $info['出生日期'] = $birthDate;

            // 计算年龄
            $today = new DateTime();
            $birth = new DateTime($birthDate);
            $age = $today->diff($birth)->y;
            $info['年龄'] = $age . '岁';

            // 计算星座
            $info['星座'] = getZodiacSignSimple($birthMonth, $birthDay);
        }

        // 提取性别 (倒数第二位数字，奇数为男，偶数为女)
        $genderCode = substr($idCard, 16, 1);
        $info['性别'] = ($genderCode % 2 == 1) ? '男' : '女';

        // 提取地区信息
        $regionCode = substr($idCard, 0, 6);
        $info['身份证归属地区'] = getRegionByCodeSimple($regionCode);
    }
    return $info;
}

// 手机号归属地查询函数（智能选择版本）
function getPhoneLocation($phone) {
    global $useAdvancedLibs;

    $info = [];

    // 如果有专业包，优先使用
    if ($useAdvancedLibs && class_exists('Shitoudev\Phone\PhoneLocation')) {
        try {
            $phoneLocation = new \Shitoudev\Phone\PhoneLocation();
            $locationInfo = $phoneLocation->find($phone);

            if (!empty($locationInfo)) {
                $info['省份'] = $locationInfo['province'] ?? '未知';
                $info['城市'] = $locationInfo['city'] ?? '未知';
                $info['运营商'] = $locationInfo['sp'] ?? '未知';
                $info['邮编'] = $locationInfo['postcode'] ?? '未知';
                $info['区号'] = $locationInfo['tel_prefix'] ?? '未知';
                $info['号段'] = substr($phone, 0, 3);
                $info['完整归属地'] = $locationInfo['province'] . ' ' . $locationInfo['city'];
            }
        } catch (Exception $e) {
            // 如果专业包出错，使用简化版本
            $info = getPhoneLocationSimple($phone);
        }
    } else {
        // 使用简化版本
        $info = getPhoneLocationSimple($phone);
    }

    return $info;
}

// 简化版手机号归属地查询（备用）
function getPhoneLocationSimple($phone) {
    // 更完整的号段数据
    $prefixes = [
        // 移动
        '134' => '移动', '135' => '移动', '136' => '移动', '137' => '移动', '138' => '移动', '139' => '移动',
        '147' => '移动', '150' => '移动', '151' => '移动', '152' => '移动', '157' => '移动', '158' => '移动',
        '159' => '移动', '172' => '移动', '178' => '移动', '182' => '移动', '183' => '移动', '184' => '移动',
        '187' => '移动', '188' => '移动', '195' => '移动', '197' => '移动', '198' => '移动',

        // 联通
        '130' => '联通', '131' => '联通', '132' => '联通', '145' => '联通', '155' => '联通', '156' => '联通',
        '166' => '联通', '167' => '联通', '171' => '联通', '175' => '联通', '176' => '联通', '185' => '联通',
        '186' => '联通', '196' => '联通',

        // 电信
        '133' => '电信', '149' => '电信', '153' => '电信', '173' => '电信', '174' => '电信', '177' => '电信',
        '180' => '电信', '181' => '电信', '189' => '电信', '191' => '电信', '193' => '电信', '199' => '电信',

        // 虚拟运营商
        '162' => '联通虚拟运营商', '165' => '移动虚拟运营商', '167' => '联通虚拟运营商',
        '170' => '虚拟运营商', '171' => '联通虚拟运营商'
    ];

    $prefix = substr($phone, 0, 3);
    $carrier = isset($prefixes[$prefix]) ? $prefixes[$prefix] : '未知运营商';

    // 简单的地区判断（基于号段的大致区域）
    $regions = [
        '138' => '上海', '139' => '北京', '136' => '广东', '137' => '江苏',
        '186' => '上海', '185' => '北京', '130' => '广东', '131' => '江苏'
    ];

    $region = isset($regions[$prefix]) ? $regions[$prefix] : '全国';

    return [
        '运营商' => $carrier,
        '号段' => $prefix,
        '大致区域' => $region
    ];
}

// 简化版星座计算函数（备用）
function getZodiacSignSimple($month, $day) {
    $zodiac = [
        ['摩羯座', 1, 20], ['水瓶座', 2, 19], ['双鱼座', 3, 21], ['白羊座', 4, 20],
        ['金牛座', 5, 21], ['双子座', 6, 22], ['巨蟹座', 7, 23], ['狮子座', 8, 23],
        ['处女座', 9, 23], ['天秤座', 10, 24], ['天蝎座', 11, 23], ['射手座', 12, 22]
    ];

    foreach ($zodiac as $i => $sign) {
        $nextIndex = ($i + 1) % 12;
        if (($month == $sign[1] && $day >= $sign[2]) ||
            ($month == $zodiac[$nextIndex][1] && $day < $zodiac[$nextIndex][2])) {
            return $sign[0];
        }
    }
    return '摩羯座'; // 默认
}

// 简化版地区代码转换函数（备用）
function getRegionByCodeSimple($code) {
    $regions = [
        '110000' => '北京市', '120000' => '天津市', '130000' => '河北省', '140000' => '山西省',
        '150000' => '内蒙古', '210000' => '辽宁省', '220000' => '吉林省', '230000' => '黑龙江省',
        '310000' => '上海市', '320000' => '江苏省', '330000' => '浙江省', '340000' => '安徽省',
        '350000' => '福建省', '360000' => '江西省', '370000' => '山东省', '410000' => '河南省',
        '420000' => '湖北省', '430000' => '湖南省', '440000' => '广东省', '450000' => '广西',
        '460000' => '海南省', '500000' => '重庆市', '510000' => '四川省', '520000' => '贵州省',
        '530000' => '云南省', '540000' => '西藏', '610000' => '陕西省', '620000' => '甘肃省',
        '630000' => '青海省', '640000' => '宁夏', '650000' => '新疆', '710000' => '台湾省',
        '810000' => '香港', '820000' => '澳门'
    ];

    $prefix = substr($code, 0, 2) . '0000';
    return isset($regions[$prefix]) ? $regions[$prefix] : '未知地区';
}

// 通过身份证查找关联的手机号
function findPhonesByIdCard($conn, $idCard, $tables) {
    $phones = [];
    foreach ($tables as $table => $indices) {
        if (isset($indices['idcard'])) {
            $idCardColumn = $indices['idcard'];
            // 查找该表中的手机号字段
            $phoneColumn = null;
            if (isset($indices['phone'])) {
                $phoneColumn = $indices['phone'];
            }

            if ($phoneColumn) {
                $sql = "SELECT DISTINCT $phoneColumn FROM $table WHERE $idCardColumn = ? AND $phoneColumn IS NOT NULL AND $phoneColumn != ''";
                $stmt = $conn->prepare($sql);
                if ($stmt) {
                    $stmt->bind_param('s', $idCard);
                    $stmt->execute();
                    $result = $stmt->get_result();
                    while ($row = $result->fetch_assoc()) {
                        $phone = $row[$phoneColumn];
                        if (!in_array($phone, $phones) && preg_match('/^1\d{10}$/', $phone)) {
                            $phones[] = $phone;
                        }
                    }
                    $stmt->close();
                }
            }
        }
    }
    return $phones;
}

// 通过手机号查找关联的身份证
function findIdCardsByPhone($conn, $phone, $tables) {
    $idCards = [];
    foreach ($tables as $table => $indices) {
        if (isset($indices['phone'])) {
            $phoneColumn = $indices['phone'];
            // 查找该表中的身份证字段
            $idCardColumn = null;
            if (isset($indices['idcard'])) {
                $idCardColumn = $indices['idcard'];
            }

            if ($idCardColumn) {
                $sql = "SELECT DISTINCT $idCardColumn FROM $table WHERE $phoneColumn = ? AND $idCardColumn IS NOT NULL AND $idCardColumn != ''";
                $stmt = $conn->prepare($sql);
                if ($stmt) {
                    $stmt->bind_param('s', $phone);
                    $stmt->execute();
                    $result = $stmt->get_result();
                    while ($row = $result->fetch_assoc()) {
                        $idCard = $row[$idCardColumn];
                        if (!in_array($idCard, $idCards) && preg_match('/^\d{17}[\dxX]$/', $idCard)) {
                            $idCards[] = $idCard;
                        }
                    }
                    $stmt->close();
                }
            }
        }
    }
    return $idCards;
}

// 获取动态参数
$msg = isset($_GET['msg']) ? $_GET['msg'] : '';
$token = isset($_GET['token']) ? $_GET['token'] : '';

// 检查参数
if (empty($msg) || empty($token)) {
    echo json_encode([
        'code' => 400,
        'message' => '请提供有效的参数和 token。'
    ], JSON_UNESCAPED_UNICODE);
    exit;
}

// 计时器开始
$startTime = microtime(true);

// 确定索引类型
$indexName = '';
if (preg_match('/^\d{18}$|^\d{17}[xX]$/', $msg)) {
    $indexName = 'idcard';
} elseif (preg_match('/^1\d{10}$/', $msg)) {
    $indexName = 'phone';
} elseif (preg_match('/^[\x{4e00}-\x{9fa5}]{2,}$/u', $msg)) {
    $indexName = 'name';
} elseif (preg_match('/^\d{6,12}$/', $msg)) { // QQ号的正则
    $indexName = 'id';
} else {
    echo json_encode([
        'code' => 400,
        'message' => '参数格式不正确。'
    ], JSON_UNESCAPED_UNICODE);
    exit;
}


include './verify_vip.php';


// 获取请求中的token
$token = $_GET['token'];  // 或者通过其他方式获取

// 自定义限制（可以自由组合）
$vipTimeLimit = false;  // 是否启用会员时间限制
$vipCodeLimit = true;  // 是否启用会员功能限制

// 定义回调函数
$callback = function($vipcode, $viptime) use ($vipTimeLimit, $vipCodeLimit) {
    if ($vipCodeLimit) {
        // 如果启用会员功能限制，执行相应的检查
        $result = vipCodeLimit($vipcode);
        if ($result !== true) return $result;  // 如果验证失败，返回错误信息
    }

    if ($vipTimeLimit) {
        // 如果启用会员时间限制，执行相应的检查
        $result = vipTimeLimit($viptime);
        if ($result !== true) return $result;  // 如果验证失败，返回错误信息
    }

    return true;  // 如果没有任何限制失败，返回true
};

// 调用验证函数
$verificationResult = verifyVipStatus($token, $callback);

// 如果返回的是错误信息，则输出
if ($verificationResult !== true) {
    echo json_encode($verificationResult, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
    exit;
}

// 查询用户信息
$stmt = $mysqli->prepare("SELECT vipcode, viptime FROM users WHERE token = ?");
$stmt->bind_param('s', $token);
$stmt->execute();
$stmt->bind_result($vipcode, $viptime);
$stmt->fetch();
$stmt->close();

// 检查会员到期时间是否大于30天
$currentDate = new DateTime();
$vipExpireDate = DateTime::createFromFormat('Y-m-d H:i:s', $viptime);
$interval = $currentDate->diff($vipExpireDate);

if ($interval->days > 35) {
    // 添加Redis限流
    $redis = new Redis();
    try {
        $redis->connect('127.0.0.1', 6379);
        $rateLimitKey = "rate_limit:{$token}";
        $lastRequestTime = $redis->get($rateLimitKey);
        
        if ($lastRequestTime) {
            $timeDiff = time() - $lastRequestTime;
            if ($timeDiff < 50) {
                echo json_encode([
                    'code' => 429,
                    'message' => '请求过于频繁，请等待' . (50 - $timeDiff) . '秒后重试'
                ], JSON_UNESCAPED_UNICODE);
                exit;
            }
        }
        
        // 设置新的请求时间
        $redis->set($rateLimitKey, time());
        $redis->expire($rateLimitKey, 60); // 设置60秒过期
        
        // 使用API进行数据检索
        $apiUrl = "http://************:8000/?msg=" . urlencode($msg);
        $apiResponse = file_get_contents($apiUrl);
        $apiResult = json_decode($apiResponse, true);
        
        if ($apiResult['code'] === 200) {
            echo json_encode([
                'code' => 200,
                'message' => '查询成功',
                'shuju' => $apiResult['shuju'],
                'execution_time' => 'API查询'
            ], JSON_UNESCAPED_UNICODE);
            exit;
        }
    } catch (Exception $e) {
        echo json_encode([
            'code' => 500,
            'message' => 'Redis连接失败: ' . $e->getMessage()
        ], JSON_UNESCAPED_UNICODE);
        exit;
    }
}

// 如果会员时间不足30天或API查询失败，使用本地数据库查询
// 连接查询数据库
$searchConn = new mysqli("localhost", "sgksjk", "sgksjk", "sgksjk");
if ($searchConn->connect_error) {
    echo json_encode([
        'code' => 500,
        'message' => '查询数据库连接失败: ' . $searchConn->connect_error
    ], JSON_UNESCAPED_UNICODE);
    exit;
}

// 定义表和索引映射
$tables = [
    "48plc" => ["idcard" => "身份证", "phone" => "手机号", "name" => "名字"],
    "chphone" => ["idcard" => "身份证", "phone" => "电话", "name" => "姓名"],
    "随申码" => ["idcard" => "身份证", "phone" => "手机号", "name" => "姓名"],
    "学习通" => ["idcard" => "身份证", "phone" => "手机号", "name" => "姓名"],
    "上海10E" => ["idcard" => "身份证","name" => "姓名"],
    "浙江学籍" => [
        "idcard" => "cardno",
        "phone" => "mobile",
        "name" => "username"
    ],
    "aurora独家数据" =>[
        "idcard" => "身份证",
        "phone" => "手机号",
        "name" => "姓名"
    ],
    "银联数据" =>[
        "idcard" => "cardno",
        "phone" => "mobile",
        "name" => "username"
    ],
    "手机源" =>[
        "phone" => "手机号"
            ],
    "学籍数据" => [
        "idcard" => "证件号码",
        "phone" => "联系电话",
        "name" => "学生姓名"
    ],
    "B站数据" => [
        "id" => "ID",
        "phone" => "手机号"
    ],
    "三要素数据" => [
        "phone" => "手机号",
        "name" => "姓名"
    ]
];

$found = false;
$messages = [];
$uniqueRows = [];
$allFoundData = []; // 存储所有找到的数据用于后续扩展

// 遍历表并根据索引查询
foreach ($tables as $table => $indices) {
    if (isset($indices[$indexName])) {
        $columns = (array) $indices[$indexName]; // 确保是数组
        foreach ($columns as $column) {
            $sql = "SELECT * FROM $table WHERE $column = ?";
            $stmt = $searchConn->prepare($sql);
            if ($stmt) {
                $stmt->bind_param('s', $msg);
                $stmt->execute();
                $result = $stmt->get_result();

                if ($result->num_rows > 0) {
                    $found = true;
                    while ($data = $result->fetch_assoc()) {
                        $rowString = implode(' | ', $data);
                        if (!in_array($rowString, $uniqueRows)) {
                            $uniqueRows[] = $rowString;

                            // 存储原始数据
                            $allFoundData[] = [
                                'table' => $table,
                                'data' => $data
                            ];

                            $message = "🏷数据标签→$table\n";
                            foreach ($data as $key => $value) {
                                $message .= "$key: $value\n";
                            }
                            $messages[] = $message;
                        }
                    }
                }
                $stmt->close();
            }
        }
    }
}

// 数据扩展处理
$enhancedInfo = [];
$collectedIdCards = [];
$collectedPhones = [];

// 收集所有身份证和手机号
foreach ($allFoundData as $item) {
    $data = $item['data'];
    foreach ($data as $key => $value) {
        if (in_array($key, ['身份证', 'cardno', '证件号码']) && !empty($value)) {
            if (!in_array($value, $collectedIdCards)) {
                $collectedIdCards[] = $value;
            }
        }
        if (in_array($key, ['手机号', 'phone', 'mobile', '电话', '联系电话']) && !empty($value)) {
            if (!in_array($value, $collectedPhones)) {
                $collectedPhones[] = $value;
            }
        }
    }
}

// 扩展身份证信息
foreach ($collectedIdCards as $idCard) {
    $idCardInfo = extractIdCardInfo($idCard);
    if (!empty($idCardInfo)) {
        $enhancedMessage = "🔍智能扩展→身份证分析 ($idCard)\n";
        foreach ($idCardInfo as $key => $value) {
            $enhancedMessage .= "$key: $value\n";
        }
        $enhancedInfo[] = $enhancedMessage;
    }
}

// 扩展手机号信息
foreach ($collectedPhones as $phone) {
    $phoneInfo = getPhoneLocation($phone);
    if (!empty($phoneInfo)) {
        $enhancedMessage = "🔍智能扩展→手机号分析 ($phone)\n";
        foreach ($phoneInfo as $key => $value) {
            $enhancedMessage .= "$key: $value\n";
        }
        $enhancedInfo[] = $enhancedMessage;
    }
}

// 进行关联查询扩展
$crossReferenceInfo = [];
if ($found) {
    // 如果找到了身份证，尝试用身份证查找更多手机号
    foreach ($collectedIdCards as $idCard) {
        $crossPhones = findPhonesByIdCard($searchConn, $idCard, $tables);
        if (!empty($crossPhones)) {
            $crossMessage = "🔗关联扩展→通过身份证 ($idCard) 找到的手机号\n";
            foreach ($crossPhones as $phone) {
                if (!in_array($phone, $collectedPhones)) {
                    $crossMessage .= "手机号: $phone\n";
                    $phoneInfo = getPhoneLocation($phone);
                    foreach ($phoneInfo as $key => $value) {
                        $crossMessage .= "  $key: $value\n";
                    }
                }
            }
            if (strpos($crossMessage, '手机号:') !== false) {
                $crossReferenceInfo[] = $crossMessage;
            }
        }
    }

    // 如果找到了手机号，尝试用手机号查找更多身份证
    foreach ($collectedPhones as $phone) {
        $crossIdCards = findIdCardsByPhone($searchConn, $phone, $tables);
        if (!empty($crossIdCards)) {
            $crossMessage = "🔗关联扩展→通过手机号 ($phone) 找到的身份证\n";
            foreach ($crossIdCards as $idCard) {
                if (!in_array($idCard, $collectedIdCards)) {
                    $crossMessage .= "身份证: $idCard\n";
                    $idCardInfo = extractIdCardInfo($idCard);
                    foreach ($idCardInfo as $key => $value) {
                        $crossMessage .= "  $key: $value\n";
                    }
                }
            }
            if (strpos($crossMessage, '身份证:') !== false) {
                $crossReferenceInfo[] = $crossMessage;
            }
        }
    }
}

// 计时器结束
$endTime = microtime(true);
$executionTime = round($endTime - $startTime, 4); // 精确到小数点后四位

if ($found) {
    // 合并所有信息
    $allMessages = array_merge($messages, $enhancedInfo, $crossReferenceInfo);

    echo json_encode([
        'code' => 200,
        'message' => '查询成功',
        'shuju' => implode("\n", $allMessages),
        'execution_time' => $executionTime . ' 秒'
    ], JSON_UNESCAPED_UNICODE);
} else {
    echo json_encode([
        'code' => 404,
        'message' => '库中无记录。',
        'execution_time' => $executionTime . ' 秒'
    ], JSON_UNESCAPED_UNICODE);
}

$searchConn->close();

?>
