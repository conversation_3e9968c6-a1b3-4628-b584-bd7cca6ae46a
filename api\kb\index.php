<?php
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');
header('Content-Type: application/json; charset=utf-8');

// 获取动态参数
$name = isset($_GET['name']) ? $_GET['name'] : '';
$idcard = isset($_GET['idcard']) ? $_GET['idcard'] : '';
$token = isset($_GET['token']) ? $_GET['token'] : '';

// 参数验证
if (empty($name) || empty($idcard) || empty($token)) {
    echo json_encode([
        'code' => 400,
        'message' => '参数缺失，请提供有效的 name、idcard 和 token。'
        
    ], JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
    exit;
}
// 检查 idcard 是否符合条件
$idcardLength = strlen($idcard);

// 确保 idcard 长度是18位（标准身份证号的长度）
if ($idcardLength === 18) {
    // 获取除了最后一个字符外的部分
    $idcardWithoutLastChar = substr($idcard, 0, 17);

    // 计算前17个字符中 'x' 的次数
    $xCount = substr_count($idcardWithoutLastChar, 'x');

    // 如果 'x' 的次数超过 8 次
    if ($xCount > 8) {
        echo json_encode([
            'code' => 400,
            'message' => '模糊位最大限制8位，idcard 中除了最后一个字符，不能有超过8个 x。'
            
        ], JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
        exit;
    }
} else {
    echo json_encode([
        'code' => 400,
        'message' => 'idcard 长度无效，应为18位。'
    ], JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
    exit;
}
if (!preg_match('/^[\x{4e00}-\x{9fa5}]{2,4}$/u', $name)) {
    echo json_encode([
        'code' => 400,
        'message' => 'name 必须是 2 到 4 个字的纯中文。'
        
    ], JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
    exit;
}
include './verify_vip.php';


// 获取请求中的token
$token = $_GET['token'];  // 或者通过其他方式获取

// 自定义限制（可以自由组合）
$vipTimeLimit = false;  // 是否启用会员时间限制
$vipCodeLimit = true;  // 是否启用会员功能限制

// 定义回调函数
$callback = function($vipcode, $viptime) use ($vipTimeLimit, $vipCodeLimit) {
    if ($vipCodeLimit) {
        // 如果启用会员功能限制，执行相应的检查
        $result = vipCodeLimit($vipcode);
        if ($result !== true) return $result;  // 如果验证失败，返回错误信息
    }

    if ($vipTimeLimit) {
        // 如果启用会员时间限制，执行相应的检查
        $result = vipTimeLimit($viptime);
        if ($result !== true) return $result;  // 如果验证失败，返回错误信息
    }

    return true;  // 如果没有任何限制失败，返回true
};

// 调用验证函数
$verificationResult = verifyVipStatus($token, $callback);

// 如果返回的是错误信息，则输出
if ($verificationResult !== true) {
    echo json_encode($verificationResult, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
    exit;
}
// 连接查询数据库
$searchConn = new mysqli("localhost", "sgksjk", "sgksjk", "sgksjk");
if ($searchConn->connect_error) {
    echo json_encode([
        'code' => 500,
        'message' => '查询数据库连接失败。'
    ], JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
    exit;
}

// 定义表和索引映射
$tables = [
    "48plc" => ["name" => "名字", "idcard" => "身份证"],
    "chphone" => ["name" => "姓名", "idcard" => "身份证"],
    "aurora独家数据" => ["name" => "姓名", "idcard" => "身份证"],
    "随申码" => ["name" => "姓名", "idcard" => "身份证"],
    "学习通" => ["name" => "姓名", "idcard" => "身份证"],
    "上海10E" => ["name" => "姓名", "idcard" => "身份证"],
    "浙江学籍" => ["name" => "username", "idcard" => "cardno"],
    "银联数据" => ["name" => "username", "idcard" => "cardno"],
];

// 优化查询逻辑
$results = [];
$idcard = str_replace('x', '%', $idcard);

foreach ($tables as $table => $columns) {
    $nameColumn = $columns['name'];
    $idcardColumn = $columns['idcard'];

    $sql = "SELECT $nameColumn AS name, $idcardColumn AS idcard 
            FROM $table 
            WHERE $idcardColumn LIKE ? AND $nameColumn LIKE ?";
    $stmt = $searchConn->prepare($sql);
    if ($stmt) {
        $likeName = "%$name%";
        $stmt->bind_param('ss', $idcard, $likeName);
        $stmt->execute();
        $result = $stmt->get_result();

        while ($row = $result->fetch_assoc()) {
            $results[] = $row;
        }
        $stmt->close();
    }
}

$searchConn->close();

if (!empty($results)) {
    // 将查询结果合并成一个以换行符分隔的文本
    $msgText = '';
    foreach ($results as $row) {
        $msgText .= "Name: " . $row['name'] . "\nIdCard: " . $row['idcard'] . "\n\n";
    }

    echo json_encode([
        'code' => 200,
        'message' => 'ok',
        'msg' => $msgText,
        '官方TG频道' => '官方TG频道@idatas8'
    ], JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
} else {
    echo json_encode([
        'code' => 404,
        'message' => '空'
    ], JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
}
?>
