<?php

include 'db.php';
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');
header('Content-Type: application/json; charset=utf-8');

// 获取动态参数
$msg = $_GET['msg'] ?? '';
$token = $_GET['token'] ?? '';

// 检查参数有效性
if (empty($msg) || empty($token)) {
    echo json_encode([
        'code' => 400,
        'message' => '请提供有效的参数和 token。'
    ], JSON_UNESCAPED_UNICODE);
    exit;
}

// 计时器开始
$startTime = microtime(true);

// 确定索引类型
$indexName = '';
if (preg_match('/^\d{18}$|^\d{17}[xX]$/', $msg)) {
    $indexName = 'idcard';
} elseif (preg_match('/^1\d{10}$/', $msg)) {
    $indexName = 'phone';
} elseif (preg_match('/^[\x{4e00}-\x{9fa5}]{2,}$/u', $msg)) {
    $indexName = 'name';
} else {
    echo json_encode([
        'code' => 400,
        'message' => '参数格式不正确。'
    ], JSON_UNESCAPED_UNICODE);
    exit;
}

include './verify_vip.php';

// VIP验证逻辑
$callback = function($vipcode, $viptime) {
    $result = vipCodeLimit($vipcode);
    if ($result !== true) return $result;
    return true;
};

$verificationResult = verifyVipStatus($token, $callback);
if ($verificationResult !== true) {
    echo json_encode($verificationResult, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
    exit;
}

$searchConn = new mysqli("localhost", "sgksjk", "sgksjk", "sgksjk");
if ($searchConn->connect_error) {
    echo json_encode([
        'code' => 500,
        'message' => '查询数据库连接失败: ' . $searchConn->connect_error
    ], JSON_UNESCAPED_UNICODE);
    exit;
}

// 定义表和字段映射
$tables = [
    "48plc" => ["idcard" => "身份证", "phone" => "手机号", "name" => "名字"],
    "chphone" => ["idcard" => "身份证", "phone" => "电话", "name" => "姓名"],
    "随申码" => ["idcard" => "身份证", "phone" => "手机号", "name" => "姓名"],
    "学习通" => ["idcard" => "身份证", "phone" => "手机号", "name" => "姓名"],
    "上海10E" => ["idcard" => "身份证", "name" => "姓名"],
    "浙江学籍" => ["idcard" => "cardno", "phone" => "mobile", "name" => "username"],
    "aurora独家数据" => ["idcard" => "身份证", "phone" => "手机号", "name" => "姓名"],
    "银联数据" => ["name" => "username", "idcard" => "cardno"]
];

// 查询结果存储
$found = false;
$messages = [];
$uniqueRows = [];

foreach ($tables as $table => $indices) {
    if (isset($indices[$indexName])) {
        $queryField = $indices[$indexName];
        $nameField = $indices['name'] ?? null;
        $idcardField = $indices['idcard'] ?? null;
        $phoneField = $indices['phone'] ?? null;

        // 只查有的字段
        $fields = [];
        if ($nameField) $fields[] = "`$nameField`";
        if ($idcardField) $fields[] = "`$idcardField`";
        if ($phoneField) $fields[] = "`$phoneField`";
        $fields = array_unique($fields);

        if (empty($fields)) continue;

        $sql = "SELECT " . implode(',', $fields) . " FROM `$table` WHERE `$queryField` = ?";
        $stmt = $searchConn->prepare($sql);
        
        if ($stmt) {
            $stmt->bind_param('s', $msg);
            $stmt->execute();
            $result = $stmt->get_result();

            if ($result->num_rows > 0) {
                $found = true;
                while ($data = $result->fetch_assoc()) {
                    $rowData = [];
                    if ($nameField && isset($data[$nameField])) $rowData[] = "姓名: {$data[$nameField]}";
                    if ($idcardField && isset($data[$idcardField])) $rowData[] = "身份证: {$data[$idcardField]}";
                    if ($phoneField && isset($data[$phoneField])) $rowData[] = "手机号: {$data[$phoneField]}";
                    $messages[] = "数据来源: $table\n" . implode(' | ', $rowData);
                }
            }
            $stmt->close();
        }
    }
}

// 返回查询结果
$executionTime = round(microtime(true) - $startTime, 4);
echo json_encode([
    'code' => $found ? 200 : 404,
    'message' => $found ? '查询成功' : '库中无记录。',
    'shuju' => $found ? implode("\n\n", $messages) : '',
    'execution_time' => $executionTime . ' 秒'
], JSON_UNESCAPED_UNICODE);

$searchConn->close();
?>