<?php
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');
header('Content-Type: application/json; charset=utf-8');

// 引入jz目录下的库
require __DIR__ . '/../jz/vendor/autoload.php';
use Shitoudev\Phone\PhoneLocation;

// 腾讯地图API KEY（可替换为你的KEY）
$tencent_map_key = 'LFJBZ-XJT3G-HFVQZ-IGOMK-YZ7RO-BKFNL';

$phone = isset($_GET['phone']) ? trim($_GET['phone']) : '';
$token = isset($_GET['token']) ? trim($_GET['token']) : '';

if (empty($phone) || empty($token)) {
    echo json_encode([
        'code' => 400,
        'message' => '请传递手机号参数phone和token'
    ], JSON_UNESCAPED_UNICODE);
    exit;
}

include './verify_vip.php';

// 会员权限校验
$vipTimeLimit = true;  // 是否启用会员时间限制
$vipCodeLimit = true;  // 是否启用会员功能限制
$callback = function($vipcode, $viptime) use ($vipTimeLimit, $vipCodeLimit) {
    if ($vipCodeLimit) {
        $result = vipCodeLimit($vipcode);
        if ($result !== true) return $result;
    }
    if ($vipTimeLimit) {
        $result = vipTimeLimit($viptime);
        if ($result !== true) return $result;
    }
    return true;
};
$verificationResult = verifyVipStatus($token, $callback);
if ($verificationResult !== true) {
    echo json_encode($verificationResult, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES);
    exit;
}

// 验证手机号格式
if (!preg_match('/^1[3-9]\d{9}$/', $phone)) {
    echo json_encode([
        'code' => 400,
        'message' => '手机号格式不正确'
    ], JSON_UNESCAPED_UNICODE);
    exit;
}

try {
    // 使用PhoneLocation库查询手机号归属地
    $phoneLocation = new PhoneLocation();
    $location = $phoneLocation->find($phone);
    
    $province = $location['province'] ?? '';
    $city = $location['city'] ?? '';
    $operator = $location['operator'] ?? '';
    
    // 2. 获取经纬度
    $geo_url = 'https://apis.map.qq.com/ws/geocoder/v1/?address=' . urlencode($city) . '&key=' . $tencent_map_key;
    $geo_json = file_get_contents($geo_url);

    $geo_data = json_decode($geo_json, true);
    if (!isset($geo_data['status']) || $geo_data['status'] != 0) {
        echo json_encode([
            'code' => 500,
            'message' => '腾讯地图API获取经纬度失败: ' . ($geo_data['message'] ?? '未知错误')
        ], JSON_UNESCAPED_UNICODE);
        exit;
    }
    $lat = $geo_data['result']['location']['lat'];
    $lng = $geo_data['result']['location']['lng'];

    // 3. 获取详细地址
    $detail_url = 'https://apis.map.qq.com/ws/geocoder/v1/?location=' . $lat . ',' . $lng . '&key=' . $tencent_map_key . '&coord_type=5&get_poi=0';
    $detail_json = file_get_contents($detail_url);

    $detail_data = json_decode($detail_json, true);
    if (!isset($detail_data['status']) || $detail_data['status'] != 0) {
        $detailed_address = '';
    } else {
        $ac = $detail_data['result']['address_component'] ?? [];
        $detailed_address = ($ac['province'] ?? '') . ($ac['city'] ?? '') . ($ac['district'] ?? '') . ($ac['street'] ?? '') . ($ac['street_number'] ?? '');
        if (trim($detailed_address) === '') {
            $detailed_address = $detail_data['result']['address'] ?? '';
        }
    }

    // 4. 模拟更逼真的手机实时定位数据
    $signal_strength = rand(-85, -45); // 信号强度 -85dBm 到 -45dBm
    $gps_accuracy = rand(5, 50); // GPS精度 5-50米
    $battery_level = rand(15, 95); // 电池电量 15%-95%
    $network_type = ['4G', '5G', '3G'][rand(0, 2)]; // 网络类型
    $cell_tower_id = 'CELL_' . rand(1000, 9999); // 基站ID
    $timestamp = time(); // 当前时间戳
    
    // 模拟GPS坐标微调（模拟真实GPS误差）
    $lat_offset = (rand(-100, 100) / 1000000); // 微调纬度
    $lng_offset = (rand(-100, 100) / 1000000); // 微调经度
    $real_lat = $lat + $lat_offset;
    $real_lng = $lng + $lng_offset;
    
    // 模拟移动速度（0-60km/h）
    $speed = rand(0, 60);
    $heading = rand(0, 359); // 方向角度 0-359度
    
    // 模拟海拔高度
    $altitude = rand(50, 500); // 海拔50-500米
    
    // 5. 生成更逼真的详细地址
    $address_parts = [];
    
    // 基础地址信息
    if (!empty($detailed_address)) {
        $address_parts[] = $detailed_address;
    }
    
    // 随机添加小区/大厦名称
    $buildings = [
        '阳光花园小区', '华润大厦', '万科广场', '万达广场', '绿地中心', 
        '保利国际广场', '恒大名都', '碧桂园', '龙湖天街', '世茂广场',
        '银泰城', '大悦城', '万象城', '凯德广场', '来福士广场',
        '远洋大厦', '富力中心', '雅居乐', '融创中心', '金茂大厦'.'领创','永福公寓','瓜沥新区','阿军面馆','城西米粉','提息体育','江会一中','南苑','胖东来',
        '烤面筋','广超玻璃','大坳','大坡脑','华鑫','凯盛一号','中国联通','中国移动','中国电信','中国广电','地平线',
        '气象骑行','智能出行'
    ];
    if (rand(0, 1)) {
        $address_parts[] = $buildings[array_rand($buildings)];
    }
    
    // 随机添加门牌号
    if (rand(0, 1)) {
        $building_number = rand(1, 999);
        $address_parts[] = "{$building_number}号";
    }
    
    // 随机添加东南西北方向距离
    $directions = ['东', '南', '西', '北', '东南', '西南', '东北', '西北'];
    $direction = $directions[array_rand($directions)];
    $distance = rand(50, 500);
    $address_parts[] = "{$direction}{$distance}米";
    
    // 随机添加附近地标
    $landmarks = [
        '地铁站附近', '公交站台旁', '学校对面', '医院旁边', '商场附近',
        '银行旁边', '加油站附近', '公园附近', '超市对面', '餐厅旁边',
        '写字楼附近', '酒店旁边', '电影院附近', '图书馆旁边', '体育馆附近',
        '咖啡厅附近', '便利店旁边', '药店附近', '理发店旁边', '洗衣店附近',
        'KTV附近', '网吧旁边', '游戏厅附近', '美容院旁边', '健身房附近',
        '宠物店附近', '花店旁边', '蛋糕店附近', '奶茶店旁边', '小吃街附近',
        '菜市场附近', '水果店旁边', '面包店附近', '服装店旁边', '鞋店附近',
        '手机店附近', '电脑城旁边', '家电卖场附近', '家具店旁边', '建材市场附近',
        '汽车4S店附近', '修车厂旁边', '洗车店附近', '停车场旁边', '公交枢纽附近',
        '火车站附近', '机场旁边', '客运站附近', '码头旁边', '高速出口附近',
        '政府大楼附近', '公安局旁边', '法院附近', '检察院旁边', '税务局附近',
        '邮局附近', '电信营业厅旁边', '移动营业厅附近', '联通营业厅旁边', '供电局附近',
        '自来水公司附近', '燃气公司旁边', '物业公司附近', '居委会旁边', '村委会附近',
        '教堂附近', '寺庙旁边', '清真寺附近', '道观旁边', '祠堂附近',
        '博物馆附近', '展览馆旁边', '科技馆附近', '天文馆旁边', '海洋馆附近',
        '动物园附近', '植物园旁边', '游乐园附近', '水上乐园旁边', '滑雪场附近',
        '高尔夫球场附近', '网球场旁边', '篮球场附近', '足球场旁边', '游泳池附近',
        '温泉附近', '按摩店旁边', '足疗店附近', 'SPA旁边', '瑜伽馆附近',
        '舞蹈教室附近', '音乐学校旁边', '美术培训班附近', '英语培训旁边', '补习班附近',
        '幼儿园附近', '托儿所旁边', '早教中心附近', '儿童乐园旁边', '母婴店附近',
        '老年活动中心附近', '养老院旁边', '康复中心附近', '体检中心旁边', '牙科诊所附近',
        '眼科医院附近', '整形医院旁边', '中医诊所附近', '针灸推拿旁边', '药店附近',
        '眼镜店附近', '珠宝店旁边', '手表店附近', '奢侈品店旁边', '二手市场附近',
        '废品回收站附近', '垃圾站旁边', '公厕附近', '垃圾桶旁边', '路灯附近',
        '监控摄像头附近', '消防栓旁边', '消防队附近', '派出所旁边', '交警队附近',
        '城管队附近', '环卫所旁边', '绿化队附近', '园林局旁边', '环保局附近',
        '气象局附近', '地震局旁边', '水利局附近', '农业局旁边', '林业局附近',
        '畜牧局附近', '渔业局旁边', '农机站附近', '种子站旁边', '农药店附近',
        '化肥店附近', '农机店旁边', '农具店附近', '饲料店旁边', '兽药店附近',
        '宠物医院附近', '兽医站旁边', '养殖场附近', '农场旁边', '果园附近',
        '菜园附近', '花园旁边', '苗圃附近', '花圃旁边', '温室附近',
        '大棚附近', '鱼塘旁边', '水库附近', '河流旁边', '湖泊附近',
        '山脚下', '山顶附近', '半山腰', '山沟里', '山谷中',
        '平原上', '丘陵地带', '盆地中', '高原上', '沙漠边缘',
        '海边附近', '沙滩旁边', '礁石附近', '港口旁边', '码头附近',
        '桥梁附近', '隧道旁边', '涵洞附近', '涵管旁边', '水渠附近',
        '水沟旁边', '水井附近', '水塔旁边', '水坝附近', '水闸旁边',
        '水电站附近', '火电厂旁边', '核电站附近', '风电场旁边', '太阳能电站附近',
        '变电站附近', '配电房旁边', '变压器附近', '电线杆旁边', '电缆沟附近',
        '通信基站附近', '信号塔旁边', '雷达站附近', '卫星地面站旁边', '天文台附近',
        '气象站附近', '地震台旁边', '水文站附近', '地质队旁边', '勘探队附近',
        '考古队附近', '文物局旁边', '博物馆附近', '纪念馆旁边', '烈士陵园附近',
        '公墓附近', '殡仪馆旁边', '火葬场附近', '骨灰堂旁边', '祠堂附近',
        '宗祠附近', '家庙旁边', '祖坟附近', '风水宝地', '龙脉附近',
        '福地附近', '宝地旁边', '吉祥之地', '祥瑞之地', '平安之地'
    ];
    if (rand(0, 1)) {
        $address_parts[] = $landmarks[array_rand($landmarks)];
    }
    
    // 组合最终地址
    $final_address = implode('', $address_parts);
    if (empty($final_address)) {
        $final_address = $detailed_address;
    }
    
    // 返回结果
    $bing_lvl = rand(12, 20);
    $bing_map_url = "https://www.bing.com/maps?cp={$real_lat}~{$real_lng}&lvl={$bing_lvl}";
    echo json_encode([
        'code' => 200,
        'message' => '查询成功[在线版仅供参考娱乐,实时数据请联系客服]',
        'data' => [
            'province' => $province,
            'city' => $city,
            'operator' => $operator,
            'lat' => $real_lat,
            'lng' => $real_lng,
            'address' => $final_address,
            'bing_map_url' => $bing_map_url,
            // 新增逼真的定位数据
            'signal_strength' => $signal_strength . 'dBm',
            'gps_accuracy' => $gps_accuracy . 'm',
            'battery_level' => $battery_level . '%',
            'network_type' => $network_type,
            'cell_tower_id' => $cell_tower_id,
            'timestamp' => $timestamp,
            'speed' => $speed . 'km/h',
            'heading' => $heading . '°',
            'altitude' => $altitude . 'm',
            'location_type' => 'GPS+基站定位',
            'last_update' => date('Y-m-d H:i:s', $timestamp)
        ]
    ], JSON_UNESCAPED_UNICODE);
    exit;
    
} catch (Exception $e) {
    echo json_encode([
        'code' => 500,
        'message' => '手机号归属地查询失败: ' . $e->getMessage()
    ], JSON_UNESCAPED_UNICODE);
    exit;
} 