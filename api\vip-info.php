<?php
/**
 * iDatas社工系统 - VIP信息查询API
 */

require_once __DIR__ . '/../includes/functions.php';
require_once __DIR__ . '/../includes/vip.php';

header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// 处理OPTIONS请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

// 获取Token
$token = $_GET['token'] ?? $_POST['token'] ?? '';

if (empty($token)) {
    jsonResponse(400, '缺少Token参数');
}

// 验证Token
if (!validateToken($token)) {
    jsonResponse(401, 'Token无效或已过期');
}

// 获取VIP状态
$vipStatus = checkVipStatus($token);

// 获取权限信息
$permissions = getVipPermissions($token);

// 获取查询限制信息
$queryLimit = checkQueryLimit($token);

// 获取查询统计
$queryStats = getUserQueryStats($token);

// 获取VIP套餐信息
$vipPackages = getVipPackages();

// 返回完整的VIP信息
jsonResponse(200, '获取VIP信息成功', [
    'vip_status' => $vipStatus,
    'permissions' => $permissions,
    'query_limit' => $queryLimit,
    'query_stats' => $queryStats,
    'vip_packages' => $vipPackages
]);
?>
